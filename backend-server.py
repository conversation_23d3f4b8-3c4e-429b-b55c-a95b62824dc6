#!/usr/bin/env python3
"""
Simple Flask backend server for order tracking
"""

from flask import Flask, jsonify
from flask_cors import CORS
import random
from datetime import datetime, timedelta

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Sample order data
SAMPLE_ORDERS = {
    "ORD001": {
        "order_id": "ORD001",
        "status": "Delivered",
        "items": ["iPhone 15", "AirPods Pro"],
        "amount": 89900,
        "created_at": "2024-01-15"
    },
    "ORD002": {
        "order_id": "ORD002", 
        "status": "Shipped",
        "items": ["MacBook Air", "Magic Mouse"],
        "amount": 119900,
        "created_at": "2024-01-16"
    },
    "ORD003": {
        "order_id": "ORD003",
        "status": "Packed", 
        "items": ["Samsung Galaxy S24", "Wireless Charger"],
        "amount": 79900,
        "created_at": "2024-01-17"
    },
    "ORD004": {
        "order_id": "ORD004",
        "status": "Placed",
        "items": ["Dell XPS 13", "USB-C Hub"],
        "amount": 99900,
        "created_at": "2024-01-18"
    },
    "TEST123": {
        "order_id": "TEST123",
        "status": "Shipped",
        "items": ["Test Product", "Sample Item"],
        "amount": 1999,
        "created_at": "2024-01-19"
    }
}

@app.route('/')
def home():
    return jsonify({
        "message": "Real-time E-commerce Order Tracking API",
        "version": "1.0.0",
        "endpoints": {
            "/order-status/<order_id>": "GET - Get order status by ID",
            "/orders": "GET - List all sample orders"
        }
    })

@app.route('/order-status/<order_id>')
def get_order_status(order_id):
    """Get order status by order ID"""
    order_id = order_id.upper()
    
    if order_id in SAMPLE_ORDERS:
        return jsonify(SAMPLE_ORDERS[order_id])
    else:
        return jsonify({
            "error": "Order not found",
            "message": f"Order ID '{order_id}' does not exist"
        }), 404

@app.route('/orders')
def list_orders():
    """List all available sample orders"""
    return jsonify({
        "orders": list(SAMPLE_ORDERS.keys()),
        "total": len(SAMPLE_ORDERS),
        "sample_data": SAMPLE_ORDERS
    })

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🚀 Starting Real-time E-commerce Order Tracking API...")
    print("📦 Sample Order IDs available:")
    for order_id, order in SAMPLE_ORDERS.items():
        print(f"   • {order_id} - {order['status']} - {', '.join(order['items'])}")
    print("\n🌐 Server will be available at: http://127.0.0.1:5002")
    print("🔗 Test in browser: http://127.0.0.1:5002/orders")
    print("\n💡 Try these order IDs in your React app:")
    print("   • ORD001 (Delivered)")
    print("   • ORD002 (Shipped)")
    print("   • ORD003 (Packed)")
    print("   • ORD004 (Placed)")
    print("   • TEST123 (Shipped)")

    app.run(host='127.0.0.1', port=5002, debug=True)
