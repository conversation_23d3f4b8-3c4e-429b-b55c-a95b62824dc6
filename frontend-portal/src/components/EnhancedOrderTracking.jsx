import { useState } from "react";

// Simple UI Components (replacing shadcn/ui)
const Card = ({ children, className = "" }) => (
  <div className={`bg-white rounded-lg border shadow-sm ${className}`}>
    {children}
  </div>
);

const CardHeader = ({ children }) => (
  <div className="p-6 pb-4">
    {children}
  </div>
);

const CardTitle = ({ children, className = "" }) => (
  <h2 className={`text-xl font-semibold ${className}`}>
    {children}
  </h2>
);

const CardContent = ({ children, className = "" }) => (
  <div className={`p-6 pt-0 ${className}`}>
    {children}
  </div>
);

const Button = ({ children, onClick, disabled = false, className = "" }) => (
  <button
    onClick={onClick}
    disabled={disabled}
    className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
  >
    {children}
  </button>
);

const Input = ({ id, placeholder, value, onChange, onKeyDown, className = "" }) => (
  <input
    id={id}
    type="text"
    placeholder={placeholder}
    value={value}
    onChange={onChange}
    onKeyDown={onKeyDown}
    className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${className}`}
  />
);

const Label = ({ htmlFor, children }) => (
  <label htmlFor={htmlFor} className="block text-sm font-medium text-gray-700 mb-1">
    {children}
  </label>
);

const Separator = () => (
  <hr className="border-gray-200 my-4" />
);

// Simple Icons (replacing lucide-react)
const Search = ({ className = "" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
);

const Package = ({ className = "" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
  </svg>
);

const Truck = ({ className = "" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM21 17a2 2 0 11-4 0 2 2 0 014 0zM13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0" />
  </svg>
);

const MapPin = ({ className = "" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
);

const CheckCircle2 = ({ className = "" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const Clock = ({ className = "" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

// Simple toast hook replacement
const useToast = () => ({
  toast: ({ title, description, variant }) => {
    const message = `${title}: ${description}`;
    if (variant === "destructive") {
      alert(`❌ ${message}`);
    } else {
      alert(`✅ ${message}`);
    }
  }
});

const EnhancedOrderTracking = () => {
  const [orderId, setOrderId] = useState("");
  const [orderStatus, setOrderStatus] = useState(null);
  const [trackingEvents, setTrackingEvents] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const statusSteps = [
    {
      key: "Placed",
      label: "Order Placed",
      icon: Package,
      description: "Order confirmed"
    },
    {
      key: "Packed",
      label: "Packed",
      icon: Package,
      description: "Items packed"
    },
    {
      key: "Shipped",
      label: "Shipped",
      icon: Truck,
      description: "Package is on its way"
    },
    {
      key: "Delivered",
      label: "Delivered",
      icon: CheckCircle2,
      description: "Package delivered"
    },
  ];

  const generateTrackingHistory = (currentStatus) => {
    const statusIndex = statusSteps.findIndex(step => step.key === currentStatus);
    const events = [];
    const now = new Date();

    for (let i = 0; i <= statusIndex; i++) {
      const step = statusSteps[i];
      const eventTime = new Date(now.getTime() - (statusIndex - i) * 24 * 60 * 60 * 1000); // Simulate past days

      let location = "Mumbai Warehouse";
      let description = step.description;

      if (step.key === "Shipped") {
        location = "Mumbai Sorting Facility";
        description = "Package departed from facility";
      } else if (step.key === "Delivered") {
        location = "Your Address";
        description = "Package delivered to recipient";
      }

      events.push({
        status: step.label,
        timestamp: eventTime.toLocaleString('en-IN', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }),
        location,
        description
      });
    }
    return events.reverse();
  };

  const getCurrentStepIndex = (status) => {
    return statusSteps.findIndex(step => step.key === status);
  };

  const trackOrder = async () => {
    if (!orderId.trim()) {
      toast({
        title: "Error",
        description: "Please enter an order ID",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    setOrderStatus(null); // Clear previous results
    try {
      // Backend API endpoint
      const response = await fetch(`http://127.0.0.1:5002/order-status/${orderId}`);

      if (!response.ok) {
        throw new Error("Order not found");
      }

      const data = await response.json();
      setOrderStatus(data);
      setTrackingEvents(generateTrackingHistory(data.status));

      toast({
        title: "Order Found",
        description: "Order status retrieved successfully",
      });
    } catch (error) {
      setOrderStatus(null);
      setTrackingEvents([]);
      toast({
        title: "Order Not Found",
        description: "Please check your order ID and try again",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Search className="h-5 w-5" />
          Track Your Order
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="order-id">Order ID</Label>
          <div className="flex gap-2">
            <Input
              id="order-id"
              placeholder="Enter your order ID"
              value={orderId}
              onChange={(e) => setOrderId(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && trackOrder()}
            />
            <Button onClick={trackOrder} disabled={isLoading}>
              {isLoading ? "Tracking..." : "Track Order"}
            </Button>
          </div>
        </div>

        {orderStatus && (
          <div className="space-y-8 animate-in fade-in-50 duration-500">
            {/* Order Summary */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-2">Order #{orderStatus.order_id}</h3>
              <p className="text-sm text-gray-600 mb-1">
                Items: {orderStatus.items.join(", ")}
              </p>
              {orderStatus.amount && (
                <p className="text-sm text-gray-600">
                  Total: ₹{orderStatus.amount.toLocaleString('en-IN')}
                </p>
              )}
            </div>

            {/* Progress Timeline */}
            <div>
              <div className="flex justify-between items-center mb-10">
                {statusSteps.map((step, index) => {
                  const currentIndex = getCurrentStepIndex(orderStatus.status);
                  const isCompleted = currentIndex >= index;
                  const Icon = step.icon;

                  return (
                    <div key={step.key} className="flex-1 flex flex-col items-center relative">
                      {/* Step Circle */}
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center z-10 border-2 ${
                        isCompleted ? "bg-blue-600 border-blue-600" : "bg-gray-100 border-gray-300"
                      }`}>
                        <Icon className={`h-5 w-5 ${isCompleted ? "text-white" : "text-gray-400"}`} />
                      </div>
                      {/* Step Label */}
                      <p className={`text-xs text-center mt-2 font-medium ${
                        isCompleted ? "text-blue-600" : "text-gray-400"
                      }`}>
                        {step.label}
                      </p>
                      {/* Connecting Line */}
                      {index < statusSteps.length - 1 && (
                        <div className={`absolute top-5 left-1/2 w-full h-1 ${
                          currentIndex > index ? "bg-blue-600" : "bg-gray-300"
                        }`} />
                      )}
                    </div>
                  );
                })}
              </div>
            </div>

            <Separator />

            {/* Tracking History */}
            <div className="space-y-4">
              <h4 className="font-medium text-lg flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Tracking History
              </h4>
              <div className="space-y-3">
                {trackingEvents.map((event, index) => (
                  <div key={index} className="flex gap-4 p-3 border-l-2 border-blue-600 relative">
                    <div className="w-4 h-4 rounded-full bg-blue-600 absolute -left-[9px] top-3"></div>
                    <div className="flex-1 space-y-1 ml-4">
                      <div className="flex justify-between items-start">
                        <h5 className="font-medium text-sm">{event.description}</h5>
                        <span className="text-xs text-gray-500">{event.timestamp}</span>
                      </div>
                      <p className="text-xs text-gray-500 flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {event.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default EnhancedOrderTracking;