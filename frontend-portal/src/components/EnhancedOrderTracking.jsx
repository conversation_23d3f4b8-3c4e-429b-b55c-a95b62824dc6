import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Search, Package, Truck, MapPin, CheckCircle2, Clock } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

// NOTE: Make sure you have created a 'hooks' folder with 'use-toast.js' if it's not there.
// If shadcn didn't create it, you might need to adjust the import path for useToast.

const EnhancedOrderTracking = () => {
  const [orderId, setOrderId] = useState("");
  const [orderStatus, setOrderStatus] = useState(null);
  const [trackingEvents, setTrackingEvents] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const statusSteps = [
    {
      key: "Placed",
      label: "Order Placed",
      icon: Package,
      description: "Order confirmed"
    },
    {
      key: "Packed",
      label: "Packed",
      icon: Package,
      description: "Items packed"
    },
    {
      key: "Shipped",
      label: "Shipped",
      icon: Truck,
      description: "Package is on its way"
    },
    {
      key: "Delivered",
      label: "Delivered",
      icon: CheckCircle2,
      description: "Package delivered"
    },
  ];

  const generateTrackingHistory = (currentStatus) => {
    const statusIndex = statusSteps.findIndex(step => step.key === currentStatus);
    const events = [];
    const now = new Date();

    for (let i = 0; i <= statusIndex; i++) {
      const step = statusSteps[i];
      const eventTime = new Date(now.getTime() - (statusIndex - i) * 24 * 60 * 60 * 1000); // Simulate past days

      let location = "Mumbai Warehouse";
      let description = step.description;

      if (step.key === "Shipped") {
        location = "Mumbai Sorting Facility";
        description = "Package departed from facility";
      } else if (step.key === "Delivered") {
        location = "Your Address";
        description = "Package delivered to recipient";
      }

      events.push({
        status: step.label,
        timestamp: eventTime.toLocaleString('en-IN', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }),
        location,
        description
      });
    }
    return events.reverse();
  };

  const getCurrentStepIndex = (status) => {
    return statusSteps.findIndex(step => step.key === status);
  };

  const trackOrder = async () => {
    if (!orderId.trim()) {
      toast({
        title: "Error",
        description: "Please enter an order ID",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    setOrderStatus(null); // Clear previous results
    try {
      // Remember to use port 5001 if you changed it
      const response = await fetch(`http://127.0.0.1:5001/order-status/${orderId}`);

      if (!response.ok) {
        throw new Error("Order not found");
      }

      const data = await response.json();
      setOrderStatus(data);
      setTrackingEvents(generateTrackingHistory(data.status));

      toast({
        title: "Order Found",
        description: "Order status retrieved successfully",
      });
    } catch (error) {
      setOrderStatus(null);
      setTrackingEvents([]);
      toast({
        title: "Order Not Found",
        description: "Please check your order ID and try again",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Search className="h-5 w-5" />
          Track Your Order
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="order-id">Order ID</Label>
          <div className="flex gap-2">
            <Input
              id="order-id"
              placeholder="Enter your order ID"
              value={orderId}
              onChange={(e) => setOrderId(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && trackOrder()}
            />
            <Button onClick={trackOrder} disabled={isLoading}>
              {isLoading ? "Tracking..." : "Track Order"}
            </Button>
          </div>
        </div>

        {orderStatus && (
          <div className="space-y-8 animate-in fade-in-50 duration-500">
            {/* Order Summary */}
            <div className="bg-muted p-4 rounded-lg">
              <h3 className="font-semibold mb-2">Order #{orderStatus.order_id}</h3>
              <p className="text-sm text-muted-foreground mb-1">
                Items: {orderStatus.items.join(", ")}
              </p>
              {orderStatus.amount && (
                <p className="text-sm text-muted-foreground">
                  Total: ₹{orderStatus.amount.toLocaleString('en-IN')}
                </p>
              )}
            </div>

            {/* Progress Timeline */}
            <div>
              <div className="flex justify-between items-center mb-10">
                {statusSteps.map((step, index) => {
                  const currentIndex = getCurrentStepIndex(orderStatus.status);
                  const isCompleted = currentIndex >= index;
                  const isCurrent = currentIndex === index;
                  const Icon = step.icon;

                  return (
                    <div key={step.key} className="flex-1 flex flex-col items-center relative">
                      {/* Step Circle */}
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center z-10 border-2 ${
                        isCompleted ? "bg-primary border-primary" : "bg-muted border-gray-300"
                      }`}>
                        <Icon className={`h-5 w-5 ${isCompleted ? "text-primary-foreground" : "text-muted-foreground"}`} />
                      </div>
                      {/* Step Label */}
                      <p className={`text-xs text-center mt-2 font-medium ${
                        isCompleted ? "text-primary" : "text-muted-foreground"
                      }`}>
                        {step.label}
                      </p>
                      {/* Connecting Line */}
                      {index < statusSteps.length - 1 && (
                        <div className={`absolute top-5 left-1/2 w-full h-1 ${
                          currentIndex > index ? "bg-primary" : "bg-gray-300"
                        }`} />
                      )}
                    </div>
                  );
                })}
              </div>
            </div>

            <Separator />

            {/* Tracking History */}
            <div className="space-y-4">
              <h4 className="font-medium text-lg flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Tracking History
              </h4>
              <div className="space-y-3">
                {trackingEvents.map((event, index) => (
                  <div key={index} className="flex gap-4 p-3 border-l-2 border-primary relative">
                    <div className="w-4 h-4 rounded-full bg-primary absolute -left-[9px] top-3"></div>
                    <div className="flex-1 space-y-1 ml-4">
                      <div className="flex justify-between items-start">
                        <h5 className="font-medium text-sm">{event.description}</h5>
                        <span className="text-xs text-muted-foreground">{event.timestamp}</span>
                      </div>
                      <p className="text-xs text-muted-foreground flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {event.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default EnhancedOrderTracking;