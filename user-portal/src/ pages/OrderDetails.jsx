import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import OrderTracking from "../components/OrderTracking";

export default function OrderDetails() {
  const { id } = useParams();
  const [order, setOrder] = useState(null);

  useEffect(() => {
    fetch(`http://127.0.0.1:5001/order-status/${id}`)
      .then((res) => res.json())
      .then((data) => setOrder(data))
      .catch(() => setOrder(null));
  }, [id]);

  if (!order) return <p className="p-6">Loading order details...</p>;

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <Link to="/" className="text-blue-500 hover:underline mb-4 inline-block">
        &larr; Back to all orders
      </Link>
      <h1 className="text-3xl font-bold mb-4">📦 Order Details</h1>
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="mb-4">
            <p className="text-sm text-gray-500">ORDER ID</p>
            <p className="font-mono">{order.order_id}</p>
        </div>
        <div className="mb-6">
            <p className="text-sm text-gray-500">ITEMS</p>
            <p className="font-semibold text-lg">{order.items.join(', ')}</p>
        </div>

        <OrderTracking status={order.status} />

        <h2 className="font-bold text-xl mt-8 mb-2">Tracking History</h2>
        <div className="border rounded-lg p-4 bg-gray-50">
          {order.history.slice().reverse().map((event, index) => (
              <div key={index} className="flex items-center mb-2">
                  <p className="font-mono text-sm text-gray-600 w-40">{event.timestamp}</p>
                  <p className="font-semibold">{event.status}</p>
              </div>
          ))}
        </div>
      </div>
    </div>
  );
}