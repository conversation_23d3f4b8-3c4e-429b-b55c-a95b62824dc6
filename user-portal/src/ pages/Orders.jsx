import { useEffect, useState } from "react";
import OrderCard from "../components/OrderCard";
import { useNavigate } from "react-router-dom";

export default function Orders() {
  const [orders, setOrders] = useState([]);
  const navigate = useNavigate();

  useEffect(() => {
    // Fetch user orders from your Flask backend. Using a hardcoded user_id for example.
    fetch("http://127.0.0.1:5001/orders?user_id=USER_101")
      .then((res) => res.json())
      .then((data) => setOrders(data.sort((a, b) => b.order_id - a.order_id))) // Show newest first
      .catch(() => setOrders([]));
  }, []);

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <h1 className="text-3xl font-bold mb-6">🛒 Your Orders</h1>
      {orders.length === 0 ? (
        <p>You haven't placed any orders yet.</p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {orders.map((order) => (
            <OrderCard
              key={order.order_id}
              order={order}
              onClick={() => navigate(`/order/${order.order_id}`)}
            />
          ))}
        </div>
      )}
    </div>
  );
}