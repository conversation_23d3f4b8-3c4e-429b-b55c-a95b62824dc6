import React from 'react';

export default function OrderCard({ order, onClick }) {
  return (
    <div
      onClick={onClick}
      className="border rounded-2xl p-4 shadow-md hover:shadow-2xl hover:scale-105 transition cursor-pointer bg-white"
    >
      <img
        src={"https://via.placeholder.com/150"} // Placeholder image
        alt="Order item"
        className="rounded-lg mb-2 w-full h-32 object-cover"
      />
      <p className="font-semibold truncate">Items: {order.items.join(', ')}</p>
      <p className="text-gray-500">₹{order.amount}</p>
      <p className="text-sm font-bold text-blue-600 mt-2">Status: {order.status}</p>
    </div>
  );
}