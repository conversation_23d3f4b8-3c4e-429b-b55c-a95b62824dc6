import React from 'react';

export default function OrderTracking({ status }) {
  const steps = ["Placed", "Packed", "Shipped", "Delivered"];
  const currentIndex = steps.findIndex(step => step.toLowerCase() === status.toLowerCase());

  return (
    <div className="w-full my-8">
      <div className="flex justify-between items-center relative">
        {/* Progress Bar */}
        <div className="absolute top-4 left-0 w-full h-1 bg-gray-200 -z-10"></div>
        <div
          className="absolute top-4 left-0 h-1 bg-green-500 -z-10 transition-all duration-500"
          style={{ width: `${(currentIndex / (steps.length - 1)) * 100}%` }}
        ></div>
        
        {/* Steps */}
        {steps.map((step, index) => (
          <div key={step} className="flex flex-col items-center z-10">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center
              ${index <= currentIndex ? "bg-green-500 text-white" : "bg-gray-300 text-gray-500"}`}
            >
              {index + 1}
            </div>
            <p className="text-xs mt-2 font-semibold">{step}</p>
          </div>
        ))}
      </div>
    </div>
  );
}