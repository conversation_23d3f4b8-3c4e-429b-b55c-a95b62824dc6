import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
// Add .jsx to the end of the import paths
import Orders from "./pages/Orders.jsx";
import OrderDetails from "./pages/OrderDetails.jsx";

export default function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Orders />} />
        <Route path="/order/:id" element={<OrderDetails />} />
      </Routes>
    </Router>
  );
}